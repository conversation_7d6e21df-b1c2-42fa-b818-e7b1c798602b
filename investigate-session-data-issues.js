/**
 * Investigate session data inconsistencies
 * Focus on sessions with 0 emails generated but have received emails or copy actions
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

async function investigateSessionDataIssues() {
  console.log('🔍 Investigating Session Data Inconsistencies...\n');

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const anonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
  
  if (!supabaseUrl || !anonKey) {
    console.log('❌ Missing Supabase credentials');
    return;
  }
  
  const supabase = createClient(supabaseUrl, anonKey);

  try {
    // 1. Check the specific session mentioned
    console.log('🎯 Checking specific session: session_1752223568604_z8fqp2cip9_dvnp4h');
    const { data: specificSession, error: specificError } = await supabase
      .from('session_analytics')
      .select('*')
      .eq('session_id', 'session_1752223568604_z8fqp2cip9_dvnp4h')
      .single();

    if (specificError) {
      console.log('❌ Error fetching specific session:', specificError.message);
    } else if (specificSession) {
      console.log('✅ Found specific session:');
      console.log(`   Emails Generated: ${specificSession.emails_generated_count}`);
      console.log(`   Emails Received: ${specificSession.emails_received_count}`);
      console.log(`   Copy Actions: ${specificSession.copy_actions_count}`);
      console.log(`   Session Start: ${specificSession.session_start_time}`);
      console.log('');
    } else {
      console.log('❌ Specific session not found');
    }

    // 2. Find all sessions with 0 emails generated but have received emails or copy actions
    console.log('🔍 Finding sessions with 0 emails generated but have activity...');
    const { data: inconsistentSessions, error: inconsistentError } = await supabase
      .from('session_analytics')
      .select('session_id, emails_generated_count, emails_received_count, copy_actions_count, session_start_time')
      .eq('emails_generated_count', 0)
      .or('emails_received_count.gt.0,copy_actions_count.gt.0')
      .order('session_start_time', { ascending: false })
      .limit(20);

    if (inconsistentError) {
      console.log('❌ Error fetching inconsistent sessions:', inconsistentError.message);
    } else {
      console.log(`✅ Found ${inconsistentSessions.length} sessions with 0 emails generated but have activity:`);
      inconsistentSessions.forEach((session, index) => {
        console.log(`   ${index + 1}. ${session.session_id}`);
        console.log(`      Generated: ${session.emails_generated_count}, Received: ${session.emails_received_count}, Copied: ${session.copy_actions_count}`);
        console.log(`      Time: ${session.session_start_time}`);
        console.log('');
      });
    }

    // 3. Check analytics_events for these sessions to see what events actually occurred
    if (inconsistentSessions && inconsistentSessions.length > 0) {
      console.log('🔍 Checking analytics_events for the first few inconsistent sessions...');
      
      for (let i = 0; i < Math.min(3, inconsistentSessions.length); i++) {
        const session = inconsistentSessions[i];
        console.log(`\n📊 Events for session: ${session.session_id}`);
        
        const { data: events, error: eventsError } = await supabase
          .from('analytics_events')
          .select('event_type, timestamp, additional_data')
          .eq('session_id', session.session_id)
          .order('timestamp', { ascending: true });

        if (eventsError) {
          console.log(`   ❌ Error fetching events: ${eventsError.message}`);
        } else {
          console.log(`   ✅ Found ${events.length} events:`);
          events.forEach((event, idx) => {
            console.log(`      ${idx + 1}. ${event.event_type} at ${event.timestamp}`);
            if (event.additional_data && event.additional_data.emailAddress) {
              console.log(`         Email: ${event.additional_data.emailAddress}`);
            }
          });
        }
      }
    }

    // 4. Check temp_emails table for recent email generations
    console.log('\n📧 Checking recent temp_emails generations...');
    const { data: recentEmails, error: emailsError } = await supabase
      .from('temp_emails')
      .select('email_address, creation_time')
      .order('creation_time', { ascending: false })
      .limit(10);

    if (emailsError) {
      console.log('❌ Error fetching temp emails:', emailsError.message);
    } else {
      console.log(`✅ Found ${recentEmails.length} recent temp emails:`);
      recentEmails.forEach((email, index) => {
        console.log(`   ${index + 1}. ${email.email_address} created at ${email.creation_time}`);
      });
    }

    // 5. Summary statistics
    console.log('\n📈 Summary Statistics:');
    
    // Total sessions
    const { count: totalSessions } = await supabase
      .from('session_analytics')
      .select('*', { count: 'exact', head: true });
    
    // Sessions with 0 generated but activity
    const { count: inconsistentCount } = await supabase
      .from('session_analytics')
      .select('*', { count: 'exact', head: true })
      .eq('emails_generated_count', 0)
      .or('emails_received_count.gt.0,copy_actions_count.gt.0');

    console.log(`   Total sessions: ${totalSessions}`);
    console.log(`   Inconsistent sessions: ${inconsistentCount}`);
    console.log(`   Inconsistency rate: ${((inconsistentCount / totalSessions) * 100).toFixed(1)}%`);

  } catch (error) {
    console.error('❌ Investigation failed:', error.message);
  }
}

// Run the investigation
investigateSessionDataIssues().catch(console.error);
