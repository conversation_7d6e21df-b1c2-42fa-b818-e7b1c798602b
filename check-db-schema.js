const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

async function checkDatabaseSchema() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const anonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
  
  if (!supabaseUrl || !anonKey) {
    console.log('Missing Supabase credentials');
    return;
  }
  
  const supabase = createClient(supabaseUrl, anonKey);
  
  console.log('📊 Checking Analytics Database Schema...\n');
  
  // Check analytics_events table structure
  try {
    const { data: analyticsEvents, error: analyticsError } = await supabase
      .from('analytics_events')
      .select('*')
      .limit(1);
    
    if (analyticsError) {
      console.log('❌ analytics_events table:', analyticsError.message);
    } else {
      console.log('✅ analytics_events table exists');
      if (analyticsEvents.length > 0) {
        console.log('   Sample columns:', Object.keys(analyticsEvents[0]));
      } else {
        console.log('   Table exists but is empty');
      }
    }
  } catch (err) {
    console.log('❌ analytics_events table error:', err.message);
  }
  
  // Check session_analytics table
  try {
    const { data: sessionData, error: sessionError } = await supabase
      .from('session_analytics')
      .select('*')
      .limit(1);
    
    if (sessionError) {
      console.log('❌ session_analytics table:', sessionError.message);
    } else {
      console.log('✅ session_analytics table exists');
      if (sessionData.length > 0) {
        console.log('   Sample columns:', Object.keys(sessionData[0]));
      } else {
        console.log('   Table exists but is empty');
      }
    }
  } catch (err) {
    console.log('❌ session_analytics table error:', err.message);
  }
  
  // Check temp_emails table
  try {
    const { data: tempEmails, error: tempError } = await supabase
      .from('temp_emails')
      .select('*')
      .limit(1);
    
    if (tempError) {
      console.log('❌ temp_emails table:', tempError.message);
    } else {
      console.log('✅ temp_emails table exists');
      if (tempEmails.length > 0) {
        console.log('   Sample columns:', Object.keys(tempEmails[0]));
      } else {
        console.log('   Table exists but is empty');
      }
    }
  } catch (err) {
    console.log('❌ temp_emails table error:', err.message);
  }
  
  // Check admin_users table
  try {
    const { data: adminUsers, error: adminError } = await supabase
      .from('admin_users')
      .select('*')
      .limit(1);
    
    if (adminError) {
      console.log('❌ admin_users table:', adminError.message);
    } else {
      console.log('✅ admin_users table exists');
      if (adminUsers.length > 0) {
        console.log('   Sample columns:', Object.keys(adminUsers[0]));
      } else {
        console.log('   Table exists but is empty');
      }
    }
  } catch (err) {
    console.log('❌ admin_users table error:', err.message);
  }
  
  // Check recent analytics data
  try {
    const { data: recentEvents, error: recentError } = await supabase
      .from('analytics_events')
      .select('event_type, timestamp')
      .order('timestamp', { ascending: false })
      .limit(5);
    
    if (!recentError && recentEvents) {
      console.log('\n📈 Recent analytics events:');
      recentEvents.forEach(event => {
        console.log(`   ${event.event_type} at ${event.timestamp}`);
      });
    }
  } catch (err) {
    console.log('\n⚠️ Could not fetch recent events:', err.message);
  }
  
  // Count total events by type
  try {
    const { data: eventCounts, error: countError } = await supabase
      .from('analytics_events')
      .select('event_type')
      .order('event_type');
    
    if (!countError && eventCounts) {
      const counts = eventCounts.reduce((acc, event) => {
        acc[event.event_type] = (acc[event.event_type] || 0) + 1;
        return acc;
      }, {});
      
      console.log('\n📊 Event type counts:');
      Object.entries(counts).forEach(([type, count]) => {
        console.log(`   ${type}: ${count}`);
      });
    }
  } catch (err) {
    console.log('\n⚠️ Could not count events:', err.message);
  }
  
  // Check if session_id field exists by trying to select it
  try {
    console.log('\n🔍 Checking for session_id field in analytics_events...');
    const { data: sessionIdTest, error: sessionIdError } = await supabase
      .from('analytics_events')
      .select('session_id')
      .limit(1);
    
    if (sessionIdError) {
      console.log('❌ session_id field does NOT exist in analytics_events table');
      console.log('   Error:', sessionIdError.message);
    } else {
      console.log('✅ session_id field EXISTS in analytics_events table');
    }
  } catch (err) {
    console.log('❌ Error checking session_id field:', err.message);
  }
}

checkDatabaseSchema().catch(console.error);
