import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import { createTempEmail, getTempEmailByAddress } from '@/lib/supabase-db';
import { EmailGenerationResponse } from '@/lib/types';
import { TIMEOUTS } from '@/lib/constants';
import { ApiError, handleApiError, logError } from '@/lib/errorHandling';
import { getDomainForEmailGeneration } from '@/lib/config/domainService';
import { getConfig } from '@/lib/config/configService';
import { logger } from '@/lib/logging/Logger';
import { TempEmail } from '@/lib/types';
// Note: updateSessionMetrics removed - handling session metrics directly in server-side code
import { createServerSupabaseClient } from '@/lib/supabase/server';

// List of common first names for generating human-like email addresses
const firstNames = [
  'john', 'jane', 'michael', 'sarah', 'david', 'emma', 'james', 'olivia',
  'robert', 'sophia', 'william', 'ava', 'joseph', 'mia', 'thomas', 'emily',
  'charles', 'amelia', 'daniel', 'ella', 'matthew', 'grace', 'anthony', 'chloe',
  'mark', 'lily', 'donald', 'hannah', 'steven', 'zoe', 'paul', 'samantha',
  'andrew', 'natalie', 'joshua', 'addison', 'kenneth', 'audrey', 'kevin', 'victoria',
  'brian', 'brooklyn', 'george', 'claire', 'timothy', 'skylar', 'ronald', 'lucy',
  'jason', 'anna', 'ryan', 'caroline', 'jacob', 'genesis', 'gary', 'kennedy',
  'nicholas', 'allison', 'eric', 'gabriella', 'jonathan', 'maya', 'stephen', 'aaliyah'
];

/**
 * Generate a human-like email address with a random string
 */
async function generateEmailAddress(): Promise<string> {
  // Select a random first name
  const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];

  // Generate a random 4-6 digit number (between 1000 and 999999)
  // This provides enough uniqueness while keeping addresses shorter
  const digitLength = Math.floor(Math.random() * 3) + 4; // Random length between 4-6 digits
  const maxValue = Math.pow(10, digitLength);
  const minValue = Math.pow(10, digitLength - 1);
  const randomDigits = Math.floor(minValue + Math.random() * (maxValue - minValue)).toString();

  // Get domain using the domain rotation logic
  const domain = await getDomainForEmailGeneration();

  await logger.info('EMAIL_GENERATION', `Generating email with domain: ${domain}`);

  return `${firstName}${randomDigits}@${domain}`;
}

/**
 * Generate a temporary email address
 *
 * @route POST /api/generate
 */
/**
 * Generate a unique email address with retry logic for collisions
 *
 * @param maxRetries Maximum number of retries if collision occurs
 * @returns A unique email address and its expiration date
 */
async function generateUniqueEmailAddress(maxRetries: number = 3): Promise<{ emailAddress: string, expirationDate: Date }> {
  // Get email expiration time from configuration
  let expirationMinutes;
  try {
    expirationMinutes = await getConfig('emailExpirationMinutes');
  } catch (configError) {
    // Fallback to default value if configuration is not found
    await logger.error('EMAIL_CONFIG', 'Error getting email expiration time from configuration, using default', { configError });
    expirationMinutes = 30; // Default: 30 minutes
  }

  // Log the expiration time for debugging
  await logger.info('EMAIL_CONFIG', `Using email expiration time: ${expirationMinutes} minutes`);

  // Set expiration date based on configuration
  const expirationDate = new Date();
  expirationDate.setMinutes(expirationDate.getMinutes() + expirationMinutes);

  // Try to generate a unique email address with retry logic
  let attempts = 0;
  let emailAddress: string;
  let tempEmail: TempEmail | null = null;

  while (attempts < maxRetries) {
    attempts++;
    emailAddress = await generateEmailAddress();

    try {
      // Check if this email address already exists
      const existingEmail = await getTempEmailByAddress(emailAddress);

      if (!existingEmail) {
        // Email address is unique, store it in the database
        tempEmail = await createTempEmail(emailAddress, expirationDate);
        await logger.info('EMAIL_GENERATION', `Generated unique email ${emailAddress} with expiration in ${expirationMinutes} minutes (attempt ${attempts})`);
        break;
      } else {
        await logger.info('EMAIL_GENERATION', `Collision detected for ${emailAddress}, retrying... (attempt ${attempts}/${maxRetries})`);
      }
    } catch (error) {
      // If there's a unique constraint violation, retry
      const errorMessage = error instanceof Error ? error.message : String(error);
      if (errorMessage.includes('unique constraint') || errorMessage.includes('duplicate key')) {
        await logger.info('EMAIL_GENERATION', `Database collision for ${emailAddress}, retrying... (attempt ${attempts}/${maxRetries})`);
      } else {
        // For other errors, rethrow
        throw error;
      }
    }
  }

  // If we couldn't generate a unique email after max retries, throw an error
  if (!tempEmail) {
    throw new Error(`Failed to generate a unique email address after ${maxRetries} attempts`);
  }

  return {
    emailAddress: tempEmail.emailAddress,
    expirationDate: tempEmail.expirationDate
  };
}

export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // Generate a unique email address with retry logic
    const { emailAddress, expirationDate } = await generateUniqueEmailAddress();

    // Track analytics server-side for 100% reliability
    await trackEmailGenerationServerSide(request, emailAddress);

    // Return the generated email address and expiration date
    const response: EmailGenerationResponse = {
      emailAddress: emailAddress,
      expirationDate: expirationDate,
      success: true
    };

    return NextResponse.json(response);
  } catch (error) {
    await logger.error('EMAIL_GENERATION', `Failed to generate email address: ${error instanceof Error ? error.message : String(error)}`, { error });

    const { message, status } = handleApiError(error);

    const errorResponse: EmailGenerationResponse = {
      emailAddress: '',
      expirationDate: new Date(),
      success: false,
      message: message || 'Failed to generate email address'
    };

    return NextResponse.json(errorResponse, { status });
  }
}

/**
 * Track email generation analytics server-side for 100% reliability
 */
async function trackEmailGenerationServerSide(request: NextRequest, emailAddress: string): Promise<void> {
  try {
    // Extract session information from request headers or body
    const sessionId = request.headers.get('x-session-id') ||
                     request.headers.get('session-id') ||
                     extractSessionFromUserAgent(request);

    if (!sessionId) {
      await logger.warning('EMAIL_GENERATION_ANALYTICS', 'No session ID found for email generation tracking', { emailAddress });
      return;
    }

    // Get client information from request
    const userAgent = request.headers.get('user-agent') || '';
    const referer = request.headers.get('referer') || '';
    const clientIP = request.headers.get('x-forwarded-for') ||
                    request.headers.get('x-real-ip') ||
                    'unknown';

    // Parse user agent for device/browser info
    const deviceType = /Mobile|Android|iPhone|iPad/.test(userAgent) ? 'mobile' : 'desktop';
    const browser = userAgent.includes('Chrome') ? 'Chrome' :
                   userAgent.includes('Firefox') ? 'Firefox' :
                   userAgent.includes('Safari') ? 'Safari' : 'Unknown';

    // Store analytics event directly in database (server-side)
    const supabase = await createServerSupabaseClient();

    const analyticsRecord = {
      event_type: 'email_address_generated',
      session_id: sessionId,
      user_id: null,
      session_start_time: new Date().toISOString(),
      session_duration: null,
      page_path: '/',
      referrer: referer || null,
      country: 'unknown', // Could be enhanced with IP geolocation
      browser: browser,
      device_type: deviceType,
      timestamp: new Date().toISOString(),
      additional_data: {
        emailAddress: emailAddress,
        domain: emailAddress.split('@')[1] || 'unknown',
        clientIP: clientIP,
        userAgent: userAgent,
        source: 'server-side'
      },
    };

    const { error: analyticsError } = await supabase
      .from('analytics_events')
      .insert(analyticsRecord);

    if (analyticsError) {
      await logger.error('EMAIL_GENERATION_ANALYTICS', 'Failed to store analytics event', {
        error: analyticsError,
        sessionId,
        emailAddress
      });
    } else {
      await logger.info('EMAIL_GENERATION_ANALYTICS', 'Successfully tracked email generation', {
        sessionId,
        emailAddress
      });
    }

    // Update session metrics directly in session_analytics table
    try {
      // First, try to find existing session
      const { data: existingSession, error: findError } = await supabase
        .from('session_analytics')
        .select('*')
        .eq('session_id', sessionId)
        .single();

      if (findError && findError.code !== 'PGRST116') { // PGRST116 = no rows found
        await logger.error('EMAIL_GENERATION_ANALYTICS', 'Error finding session', {
          error: findError,
          sessionId
        });
      } else if (existingSession) {
        // Update existing session
        const { error: updateError } = await supabase
          .from('session_analytics')
          .update({
            emails_generated_count: (existingSession.emails_generated_count || 0) + 1
          })
          .eq('session_id', sessionId);

        if (updateError) {
          await logger.error('EMAIL_GENERATION_ANALYTICS', 'Failed to update session metrics', {
            error: updateError,
            sessionId
          });
        } else {
          await logger.info('EMAIL_GENERATION_ANALYTICS', 'Updated session metrics', {
            sessionId,
            newCount: (existingSession.emails_generated_count || 0) + 1
          });
        }
      } else {
        // Create new session
        const { error: insertError } = await supabase
          .from('session_analytics')
          .insert({
            session_id: sessionId,
            session_start_time: new Date().toISOString(),
            emails_generated_count: 1,
            emails_received_count: 0,
            emails_viewed_count: 0,
            copy_actions_count: 0,
            manual_refresh_count: 0
          });

        if (insertError) {
          await logger.error('EMAIL_GENERATION_ANALYTICS', 'Failed to create session metrics', {
            error: insertError,
            sessionId
          });
        } else {
          await logger.info('EMAIL_GENERATION_ANALYTICS', 'Created new session metrics', {
            sessionId
          });
        }
      }
    } catch (sessionError) {
      await logger.error('EMAIL_GENERATION_ANALYTICS', 'Unexpected error in session metrics', {
        error: sessionError,
        sessionId
      });
    }

  } catch (error) {
    await logger.error('EMAIL_GENERATION_ANALYTICS', 'Error in server-side email generation tracking', {
      error,
      emailAddress
    });
  }
}

/**
 * Extract session ID from user agent or generate a fallback
 */
function extractSessionFromUserAgent(request: NextRequest): string {
  // This is a fallback - ideally session ID should come from headers
  const userAgent = request.headers.get('user-agent') || '';
  const timestamp = Date.now();
  const hash = userAgent.slice(-10); // Last 10 chars of user agent as simple hash
  return `server_session_${timestamp}_${hash}`;
}

/**
 * Check if an email address exists and is valid
 *
 * @route GET /api/generate?email=<EMAIL>
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    // Get the email address from the query parameters
    const { searchParams } = new URL(request.url);
    const emailAddress = searchParams.get('email');

    if (!emailAddress) {
      return NextResponse.json({
        success: false,
        message: 'Email address is required'
      }, { status: 400 });
    }

    // Check if the email address exists in the database
    const tempEmail = await getTempEmailByAddress(emailAddress);

    if (!tempEmail) {
      return NextResponse.json({
        success: false,
        message: 'Email address not found'
      }, { status: 404 });
    }

    // Check if the email address has expired
    const now = new Date();
    if (tempEmail.expirationDate < now) {
      return NextResponse.json({
        success: false,
        message: 'Email address has expired'
      }, { status: 410 });
    }

    // Return the email address and expiration date
    const response: EmailGenerationResponse = {
      emailAddress: tempEmail.emailAddress,
      expirationDate: tempEmail.expirationDate,
      success: true
    };

    return NextResponse.json(response);
  } catch (error) {
    await logger.error('EMAIL_VALIDATION', `Failed to check email address: ${error instanceof Error ? error.message : String(error)}`, { error });

    const { message, status } = handleApiError(error);

    return NextResponse.json({
      success: false,
      message: message || 'Failed to check email address'
    }, { status });
  }
}
