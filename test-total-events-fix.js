/**
 * Test the Total Events card fix specifically
 */

async function testTotalEventsFix() {
  console.log('🔍 Testing Total Events Card Fix...\n');

  try {
    // 1. First, verify database reality
    const { createClient } = require('@supabase/supabase-js');
    require('dotenv').config({ path: '.env.local' });

    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const anonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
    
    if (!supabaseUrl || !anonKey) {
      console.log('❌ Missing Supabase credentials');
      return;
    }
    
    const supabase = createClient(supabaseUrl, anonKey);

    // Get actual total events from database
    const { count: actualTotalEvents, error: countError } = await supabase
      .from('analytics_events')
      .select('*', { count: 'exact', head: true });

    if (countError) {
      console.log('❌ Error getting actual total events:', countError.message);
      return;
    }

    console.log(`📊 Database Reality: ${actualTotalEvents} total events`);

    // 2. Test the API endpoint that should return all events
    console.log('\n🔍 Testing API endpoint for all events...');
    
    try {
      const allEventsResponse = await fetch('http://localhost:3000/api/management-portal-x7z9y2/analytics?timeRange=all&aggregation=count');
      
      if (!allEventsResponse.ok) {
        console.log(`❌ API request failed with status: ${allEventsResponse.status}`);
        const errorText = await allEventsResponse.text();
        console.log(`   Error response: ${errorText.substring(0, 200)}...`);
        return;
      }

      const allEventsResult = await allEventsResponse.json();
      
      if (!allEventsResult.success) {
        console.log('❌ API returned error:', allEventsResult.error);
        return;
      }

      console.log('✅ All Events API Response:');
      console.log(`   Total Events: ${allEventsResult.data.totalEvents}`);
      console.log(`   Event Counts:`, allEventsResult.data.eventCounts);
      
      // Check if API matches database
      if (allEventsResult.data.totalEvents === actualTotalEvents) {
        console.log('✅ API matches database count!');
      } else {
        console.log(`❌ API mismatch! API: ${allEventsResult.data.totalEvents}, DB: ${actualTotalEvents}`);
      }

    } catch (apiError) {
      console.log('❌ API request failed:', apiError.message);
      
      if (apiError.code === 'ECONNREFUSED') {
        console.log('\n💡 Development server not running. Starting test with direct database check only...');
      }
      return;
    }

    // 3. Test different time ranges to see the difference
    console.log('\n📅 Testing different time ranges...');
    
    const timeRanges = ['24h', '7d', '30d', 'all'];
    for (const timeRange of timeRanges) {
      try {
        const response = await fetch(`http://localhost:3000/api/management-portal-x7z9y2/analytics?timeRange=${timeRange}&aggregation=count`);
        if (response.ok) {
          const result = await response.json();
          if (result.success) {
            console.log(`   ${timeRange.padEnd(4)}: ${result.data.totalEvents} events`);
          }
        }
      } catch (err) {
        console.log(`   ${timeRange.padEnd(4)}: API error`);
      }
    }

    // 4. Check what the RealTimeMetrics component should be getting
    console.log('\n🎯 Expected RealTimeMetrics behavior:');
    console.log(`   Should fetch: /api/management-portal-x7z9y2/analytics?timeRange=all&aggregation=count`);
    console.log(`   Should get: ${actualTotalEvents} events`);
    console.log(`   Should display: ${(actualTotalEvents / 1000).toFixed(1)}K`);

    // 5. Check if there are any console errors or issues
    console.log('\n🔧 Troubleshooting checklist:');
    console.log('   ✅ Database has correct count');
    console.log('   ✅ API endpoint supports timeRange=all');
    console.log('   ✅ RealTimeMetrics component updated to use totalEventsAllTime');
    console.log('   ⚠️  Need to verify: Is the component actually calling the API?');
    console.log('   ⚠️  Need to verify: Is there any error in the browser console?');
    console.log('   ⚠️  Need to verify: Is the component state updating correctly?');

    console.log('\n💡 Next steps:');
    console.log('   1. Check browser console for any JavaScript errors');
    console.log('   2. Verify the RealTimeMetrics component is making the API call');
    console.log('   3. Check if there are any caching issues');
    console.log('   4. Ensure the development server has the latest code');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testTotalEventsFix().catch(console.error);
