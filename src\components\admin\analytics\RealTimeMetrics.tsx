'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import { Activity, Clock, TrendingUp, Users, RefreshCw } from 'lucide-react';
import LiveVisitorCount from './LiveVisitorCount';

interface RecentEvent {
  id: string;
  event_type: string;
  timestamp: string;
  session_id: string;
  additional_data?: any;
  device_type?: string;
  browser?: string;
}

interface RealTimeStats {
  activeSessionsLast5Min: number;
  eventsLast5Min: number;
  eventsLast1Min: number;
  topEventTypesLast5Min: Record<string, number>;
  totalEventsAllTime: number; // Add total events count
}

interface AnalyticsData {
  totalEvents: number;
  eventCounts: Record<string, number>;
  timeRange: {
    startDate: string;
    endDate: string;
  };
}

interface RealTimeMetricsProps {
  analyticsData?: AnalyticsData;
  isLoading?: boolean;
}

/**
 * Real Time Metrics Component
 *
 * Displays live analytics data including recent events,
 * active sessions, and real-time statistics.
 */
export default function RealTimeMetrics({ analyticsData, isLoading: parentLoading = false }: RealTimeMetricsProps = {}) {
  const [recentEvents, setRecentEvents] = useState<RecentEvent[]>([]);
  const [realTimeStats, setRealTimeStats] = useState<RealTimeStats>({
    activeSessionsLast5Min: 0,
    eventsLast5Min: 0,
    eventsLast1Min: 0,
    topEventTypesLast5Min: {},
    totalEventsAllTime: 0
  });
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  /**
   * Fetch real-time analytics data
   */
  const fetchRealTimeData = async () => {
    try {
      setError(null);

      // Get events from the last 5 minutes
      const now = new Date().toISOString();
      const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000).toISOString();
      const oneMinuteAgo = new Date(Date.now() - 1 * 60 * 1000).toISOString();

      // Fetch recent events with both start and end date for precise 5-minute window
      const eventsResponse = await fetch(
        `/api/management-portal-x7z9y2/analytics?startDate=${fiveMinutesAgo}&endDate=${now}&aggregation=count`
      );

      if (!eventsResponse.ok) {
        throw new Error(`Events API error: ${eventsResponse.status}`);
      }

      const eventsResult = await eventsResponse.json();

      if (!eventsResult.success) {
        throw new Error(eventsResult.error || 'Failed to fetch events data');
      }

      // Fetch total events count (all time) for the Total Events card
      const totalEventsResponse = await fetch(
        `/api/management-portal-x7z9y2/analytics?timeRange=all&aggregation=count`
      );

      let totalEventsAllTime = 0;
      if (totalEventsResponse.ok) {
        const totalEventsResult = await totalEventsResponse.json();
        if (totalEventsResult.success) {
          totalEventsAllTime = totalEventsResult.data.totalEvents || 0;
        }
      }

      // Fetch session data for active sessions with precise 5-minute window
      const sessionsResponse = await fetch(
        `/api/management-portal-x7z9y2/analytics/sessions?startDate=${fiveMinutesAgo}&endDate=${now}&limit=50`
      );
      
      if (!sessionsResponse.ok) {
        throw new Error(`Sessions API error: ${sessionsResponse.status}`);
      }

      const sessionsResult = await sessionsResponse.json();
      
      if (!sessionsResult.success) {
        throw new Error(sessionsResult.error || 'Failed to fetch sessions data');
      }

      // Calculate real-time stats with proper fallbacks for disabled session analytics
      const stats: RealTimeStats = {
        activeSessionsLast5Min: sessionsResult.data?.summary?.totalSessions || 0,
        eventsLast5Min: eventsResult.data.totalEvents || 0,
        eventsLast1Min: 0, // This would need a separate API call for 1-minute data
        topEventTypesLast5Min: eventsResult.data.eventCounts || {},
        totalEventsAllTime: totalEventsAllTime
      };

      // Debug logging to help track the issue
      console.log('Real-time metrics update:', {
        timeWindow: `${fiveMinutesAgo} to ${now}`,
        eventsFound: stats.eventsLast5Min,
        eventTypes: Object.keys(stats.topEventTypesLast5Min),
        eventCounts: stats.topEventTypesLast5Min
      });

      setRealTimeStats(stats);
      setLastUpdate(new Date());

    } catch (error) {
      console.error('Error fetching real-time data:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch real-time data');
    } finally {
      setIsLoading(false);
    }
  };

  // Initial data fetch
  useEffect(() => {
    fetchRealTimeData();
  }, []);

  // Auto-refresh every 15 seconds for more real-time updates
  useEffect(() => {
    const interval = setInterval(fetchRealTimeData, 15 * 1000);
    return () => clearInterval(interval);
  }, []);

  /**
   * Format event type for display
   */
  const formatEventType = (eventType: string): string => {
    // Custom formatting for specific event types
    const customNames: Record<string, string> = {
      'email_received': 'Email Received',
      'emails_received_count': 'Email Received', // Legacy - should be rare now
      'email_address_generated': 'Email Generated',
      'email_address_copied': 'Email Copied',
      'email_opened': 'Email Opened',
      'manual_refresh_triggered': 'Manual Refresh',
      'session_start': 'Session Started',
      'session_end': 'Session Ended'
    };

    if (customNames[eventType]) {
      return customNames[eventType];
    }

    // Fallback to automatic formatting
    return eventType
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  /**
   * Format large numbers with appropriate suffixes
   */
  const formatNumber = (value: number): string => {
    if (value >= 1000000) {
      return `${(value / 1000000).toFixed(1)}M`;
    } else if (value >= 1000) {
      return `${(value / 1000).toFixed(1)}K`;
    } else {
      return value.toString();
    }
  };

  /**
   * Get event type color
   */
  const getEventTypeColor = (eventType: string): string => {
    const colors: Record<string, string> = {
      'session_start': 'bg-blue-100 text-blue-800',
      'email_address_generated': 'bg-green-100 text-green-800',
      'email_received': 'bg-blue-100 text-blue-800',
      'emails_received_count': 'bg-blue-100 text-blue-800',
      'email_address_copied': 'bg-orange-100 text-orange-800',
      'email_opened': 'bg-purple-100 text-purple-800',
      'email_deleted': 'bg-red-100 text-red-800',
      'manual_refresh_triggered': 'bg-cyan-100 text-cyan-800',
      'session_end': 'bg-gray-100 text-gray-800'
    };
    
    return colors[eventType] || 'bg-gray-100 text-gray-800';
  };

  /**
   * Format time ago
   */
  const formatTimeAgo = (timestamp: string): string => {
    const now = new Date();
    const eventTime = new Date(timestamp);
    const diffInSeconds = Math.floor((now.getTime() - eventTime.getTime()) / 1000);
    
    if (diffInSeconds < 60) {
      return `${diffInSeconds}s ago`;
    } else if (diffInSeconds < 3600) {
      return `${Math.floor(diffInSeconds / 60)}m ago`;
    } else {
      return `${Math.floor(diffInSeconds / 3600)}h ago`;
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="animate-pulse">
              <div className="h-20 bg-gray-200 rounded-lg"></div>
            </div>
          ))}
        </div>
        <div className="animate-pulse">
          <div className="h-64 bg-gray-200 rounded-lg"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600 font-medium">Error loading real-time data</p>
        <p className="text-gray-500 text-sm mt-1">{error}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Live Visitor Count - Prominent Display */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <div className="lg:col-span-1">
          <LiveVisitorCount refreshInterval={15000} activeThreshold={3} />
        </div>

        {/* Real-time Stats Cards */}
        <div className="lg:col-span-3">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Active Sessions</p>
                    <p className="text-2xl font-bold text-blue-600">
                      {realTimeStats.activeSessionsLast5Min}
                    </p>
                    <p className="text-xs text-gray-500">Last 5 minutes</p>
                  </div>
                  <Users className="h-8 w-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Total Events</p>
                    <p className="text-2xl font-bold text-green-600">
                      {analyticsData ? (() => {
                        console.log('🔍 Total Events Debug:', {
                          rawValue: analyticsData.totalEvents,
                          formatted: formatNumber(analyticsData.totalEvents),
                          type: typeof analyticsData.totalEvents
                        });
                        return formatNumber(analyticsData.totalEvents);
                      })() : '0'}
                    </p>
                    <p className="text-xs text-gray-500">All tracked interactions</p>
                  </div>
                  <Activity className="h-8 w-8 text-green-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Events/Minute</p>
                    <p className="text-2xl font-bold text-purple-600">
                      {Math.round(realTimeStats.eventsLast5Min / 5)}
                    </p>
                    <p className="text-xs text-gray-500">Average rate</p>
                  </div>
                  <TrendingUp className="h-8 w-8 text-purple-600" />
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Second row with Last Update card */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <div className="lg:col-span-1">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Last Update</p>
                  <p className="text-lg font-bold text-gray-600">
                    {lastUpdate.toLocaleTimeString('en-US', {
                      hour: '2-digit',
                      minute: '2-digit',
                      second: '2-digit'
                    })}
                  </p>
                  <p className="text-xs text-gray-500">Auto-refresh: 15s</p>
                </div>
                <Clock className="h-8 w-8 text-gray-600" />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Top Event Types */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">Top Event Types (Last 5 Minutes)</CardTitle>
            <Button
              onClick={fetchRealTimeData}
              disabled={isLoading}
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0"
              title="Refresh real-time data"
            >
              <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {Object.keys(realTimeStats.topEventTypesLast5Min).length === 0 ? (
            <p className="text-gray-500 text-center py-4">No events in the last 5 minutes</p>
          ) : (
            <div className="space-y-3">
              {Object.entries(realTimeStats.topEventTypesLast5Min)
                .sort(([, a], [, b]) => b - a)
                .slice(0, 5)
                .map(([eventType, count]) => (
                  <div key={eventType} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Badge className={getEventTypeColor(eventType)}>
                        {formatEventType(eventType)}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{count}</span>
                      <div className="w-16 bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-blue-600 h-2 rounded-full" 
                          style={{ 
                            width: `${(count / Math.max(...Object.values(realTimeStats.topEventTypesLast5Min))) * 100}%` 
                          }}
                        ></div>
                      </div>
                    </div>
                  </div>
                ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* System Status */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">System Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center gap-3">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span className="text-sm">Analytics API</span>
              <Badge variant="outline" className="text-green-600 border-green-600">
                Online
              </Badge>
            </div>
            
            <div className="flex items-center gap-3">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span className="text-sm">Database</span>
              <Badge variant="outline" className="text-green-600 border-green-600">
                Connected
              </Badge>
            </div>
            
            <div className="flex items-center gap-3">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span className="text-sm">Real-time Updates</span>
              <Badge variant="outline" className="text-green-600 border-green-600">
                Active
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
