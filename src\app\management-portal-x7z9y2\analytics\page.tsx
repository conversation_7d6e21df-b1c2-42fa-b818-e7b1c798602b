'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/Select';
import {
  CalendarIcon,
  ArrowPathIcon as RefreshCw,
  ChartBarIcon as TrendingUp,
  UsersIcon as Users,
  EnvelopeIcon as Mail,
  CursorArrowRaysIcon as MousePointer,
  ClockIcon as Clock,
  EyeIcon as Eye
} from '@heroicons/react/24/outline';
import AnalyticsOverview from '@/components/admin/analytics/AnalyticsOverview';
import EventTimeSeriesChart from '@/components/admin/analytics/EventTimeSeriesChart';
import SessionAnalyticsChart from '@/components/admin/analytics/SessionAnalyticsChart';
import RealTimeMetrics from '@/components/admin/analytics/RealTimeMetrics';
import TimeRangeSelector from '@/components/admin/analytics/TimeRangeSelector';
import ClearAnalyticsButton from '@/components/admin/analytics/ClearAnalyticsButton';

interface AnalyticsData {
  totalEvents: number;
  eventCounts: Record<string, number>;
  timeRange: {
    startDate: string;
    endDate: string;
  };
}

interface SessionData {
  summary: {
    totalSessions: number;
    avgSessionDuration: number;
    totalEmailsGenerated: number;
    totalEmailsReceived: number;
    totalEmailsViewed: number;
    totalEmailsDeleted: number;
    totalManualRefreshes: number;
    totalCopyActions: number;
  };
  engagement: {
    avgEmailsGeneratedPerSession: number;
    avgEmailsViewedPerSession: number;
    avgManualRefreshesPerSession: number;
    avgCopyActionsPerSession: number;
    emailViewRate: number;
    emailDeleteRate: number;
  };
  sessions?: any[];
  breakdowns: {
    device: Record<string, number>;
    browser: Record<string, number>;
    country: Record<string, number>;
    sessionDuration: Record<string, number>;
  };
}

/**
 * Analytics Dashboard Page
 * 
 * Main analytics dashboard for VanishPost admin portal.
 * Displays comprehensive analytics data with various visualizations.
 */
export default function AnalyticsPage() {
  const [timeRange, setTimeRange] = useState<string>('24h');
  const [customDateRange, setCustomDateRange] = useState<{ start: string; end: string } | undefined>(undefined);
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [sessionData, setSessionData] = useState<SessionData | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());

  /**
   * Fetch analytics data from the API
   */
  const fetchAnalyticsData = async (selectedTimeRange: string, customRange?: { start: string; end: string }) => {
    try {
      setIsLoading(true);
      setError(null);

      // Build query parameters
      const params = new URLSearchParams();
      
      if (customRange) {
        params.append('startDate', customRange.start);
        params.append('endDate', customRange.end);
      } else {
        params.append('timeRange', selectedTimeRange);
      }
      
      params.append('aggregation', 'count');

      // Fetch main analytics data
      const analyticsResponse = await fetch(`/api/management-portal-x7z9y2/analytics?${params.toString()}`);
      
      if (!analyticsResponse.ok) {
        throw new Error(`Analytics API error: ${analyticsResponse.status}`);
      }

      const analyticsResult = await analyticsResponse.json();
      
      if (!analyticsResult.success) {
        throw new Error(analyticsResult.error || 'Failed to fetch analytics data');
      }

      setAnalyticsData(analyticsResult.data);

      // Fetch session analytics data
      const sessionParams = new URLSearchParams();
      if (customRange) {
        sessionParams.append('startDate', customRange.start);
        sessionParams.append('endDate', customRange.end);
      } else {
        sessionParams.append('timeRange', selectedTimeRange);
      }
      sessionParams.append('limit', '100');

      const sessionResponse = await fetch(`/api/management-portal-x7z9y2/analytics/sessions?${sessionParams.toString()}`);
      
      if (!sessionResponse.ok) {
        throw new Error(`Session API error: ${sessionResponse.status}`);
      }

      const sessionResult = await sessionResponse.json();
      
      if (!sessionResult.success) {
        throw new Error(sessionResult.error || 'Failed to fetch session data');
      }

      // Since session analytics is temporarily disabled, provide default structure
      const defaultSessionData = {
        summary: {
          totalSessions: 0,
          avgSessionDuration: 0,
          totalEmailsGenerated: 0,
          totalEmailsReceived: 0,
          totalEmailsViewed: 0,
          totalEmailsDeleted: 0,
          totalManualRefreshes: 0,
          totalCopyActions: 0
        },
        engagement: {
          avgEmailsGeneratedPerSession: 0,
          avgEmailsViewedPerSession: 0,
          avgManualRefreshesPerSession: 0,
          avgCopyActionsPerSession: 0,
          emailViewRate: 0,
          emailDeleteRate: 0
        },
        breakdowns: {
          device: {},
          browser: {},
          country: {},
          sessionDuration: {}
        }
      };

      setSessionData(sessionResult.data?.summary ? sessionResult.data : defaultSessionData);
      setLastRefresh(new Date());

    } catch (error) {
      console.error('Error fetching analytics data:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch analytics data');
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Handle time range change
   */
  const handleTimeRangeChange = (newTimeRange: string) => {
    setTimeRange(newTimeRange);
    setCustomDateRange(undefined);
    fetchAnalyticsData(newTimeRange);
  };

  /**
   * Handle custom date range change
   */
  const handleCustomDateRangeChange = (start: string, end: string) => {
    const customRange = { start, end };
    setCustomDateRange(customRange);
    setTimeRange('custom');
    fetchAnalyticsData('custom', customRange);
  };

  /**
   * Handle manual refresh
   */
  const handleRefresh = () => {
    if (customDateRange) {
      fetchAnalyticsData('custom', customDateRange);
    } else {
      fetchAnalyticsData(timeRange);
    }
  };

  // Initial data fetch
  useEffect(() => {
    fetchAnalyticsData(timeRange);
  }, []);

  // Auto-refresh every 5 minutes
  useEffect(() => {
    const interval = setInterval(() => {
      if (customDateRange) {
        fetchAnalyticsData('custom', customDateRange);
      } else {
        fetchAnalyticsData(timeRange);
      }
    }, 5 * 60 * 1000); // 5 minutes

    return () => clearInterval(interval);
  }, [timeRange, customDateRange]);

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Analytics Dashboard</h1>
          <p className="text-gray-600 mt-1">
            VanishPost Email Generator Analytics
          </p>
        </div>
        
        <div className="flex flex-col sm:flex-row gap-3">
          <TimeRangeSelector
            value={timeRange}
            onChange={handleTimeRangeChange}
            onCustomRangeChange={handleCustomDateRangeChange}
          />
          
          <Button
            onClick={handleRefresh}
            disabled={isLoading}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Last Refresh Time */}
      <div className="text-sm text-gray-500">
        Last updated: {lastRefresh.toLocaleString()}
      </div>

      {/* Error State */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-red-700">
              <span className="font-medium">Error:</span>
              <span>{error}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Loading State */}
      {isLoading && !analyticsData && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="pb-2">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Analytics Content */}
      {!isLoading && analyticsData && sessionData && (
        <>
          {/* Overview Cards */}
          <AnalyticsOverview 
            analyticsData={analyticsData}
            sessionData={sessionData}
            isLoading={isLoading}
          />

          {/* Charts Section */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Event Time Series Chart */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Event Timeline
                </CardTitle>
                <CardDescription>
                  Events over time for the selected period
                </CardDescription>
              </CardHeader>
              <CardContent>
                <EventTimeSeriesChart 
                  timeRange={timeRange}
                  customDateRange={customDateRange}
                />
              </CardContent>
            </Card>

            {/* Session Analytics Chart */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Session Analytics
                </CardTitle>
                <CardDescription>
                  Session duration and engagement metrics
                </CardDescription>
              </CardHeader>
              <CardContent>
                <SessionAnalyticsChart 
                  sessionData={sessionData}
                />
              </CardContent>
            </Card>
          </div>

          {/* Real-time Metrics */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Real-time Metrics
              </CardTitle>
              <CardDescription>
                Live analytics data and recent activity
              </CardDescription>
            </CardHeader>
            <CardContent>
              <RealTimeMetrics
                analyticsData={analyticsData}
                isLoading={isLoading}
              />
            </CardContent>
          </Card>

          {/* Device and Browser Breakdown */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Device Breakdown</CardTitle>
                <CardDescription>
                  User device distribution
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {Object.entries(sessionData.breakdowns.device).map(([device, count]) => (
                    <div key={device} className="flex justify-between items-center">
                      <span className="capitalize">{device}</span>
                      <span className="font-medium">{count}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Browser Breakdown</CardTitle>
                <CardDescription>
                  User browser distribution
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {Object.entries(sessionData.breakdowns.browser).map(([browser, count]) => (
                    <div key={browser} className="flex justify-between items-center">
                      <span>{browser}</span>
                      <span className="font-medium">{count}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Clear Analytics Data Section */}
          <div className="mt-8 pt-8 border-t border-gray-200">
            <ClearAnalyticsButton
              onDataCleared={() => {
                // Refresh all data after clearing
                fetchAnalyticsData(timeRange, customDateRange);
              }}
            />
          </div>
        </>
      )}
    </div>
  );
}
