/**
 * Custom hook for managing email selection
 */

import { useState, useCallback, useRef } from 'react';
import { Email } from '@/lib/emailProcessing';
import { STORAGE, TIMEOUTS } from '@/lib/constants';
import { logError } from '@/lib/errorHandling';
import AutoRefreshManager from '@/lib/AutoRefreshManager';
import { trackEmailOpened, trackEmailDeleted } from '@/lib/analytics/emailGeneratorAnalytics';

interface EmailSelectionResult {
  /** Currently selected email */
  selectedEmail: Email | null;

  /** Function to select an email */
  selectEmail: (emailId: string) => void;

  /** Function to mark an email as read */
  markAsRead: (emailId: string) => void;

  /** Function to delete an email */
  deleteEmail: (emailId: string, e?: React.MouseEvent) => void;

  /** Function to load read status from localStorage */
  loadReadStatus: () => Record<string, boolean>;

  /** Function to save read status to localStorage */
  saveReadStatus: (emailId: string, isRead: boolean) => void;

  /** Function to clear the selected email */
  clearSelectedEmail: () => void;

  /** Function to abort any in-flight delete requests */
  abortDeleteRequests: () => void;

  /** Function to clear the deleted emails set when generating a new address */
  clearDeletedEmailsSet: () => void;

  /** Function to filter out deleted emails from a list */
  filterOutDeletedEmails: (emails: Email[]) => Email[];

  /** Function to filter out emails from a previous address during transition */
  filterDuringAddressTransition: (emails: Email[], currentAddress: string | null, previousAddress: string | null) => Email[];
}

/**
 * Custom hook for managing email selection
 */
export const useEmailSelection = (
  emails: Email[],
  setEmails: React.Dispatch<React.SetStateAction<Email[]>>,
  sessionId?: string,
  sessionInitialized?: boolean
): EmailSelectionResult => {
  // State for the selected email
  const [selectedEmail, setSelectedEmail] = useState<Email | null>(null);

  // Ref for the abort controller used for delete requests
  const deleteAbortControllerRef = useRef<AbortController | null>(null);

  // Ref to track deleted email IDs to prevent them from reappearing
  const deletedEmailIdsRef = useRef<Set<string>>(new Set());

  // Ref to track if a deletion is in progress (mutex lock)
  const isDeletingRef = useRef<boolean>(false);

  // Ref to track pending deletion queue
  const pendingDeletionsRef = useRef<{emailId: string, resolve: () => void, reject: (error: Error) => void}[]>([]);

  // Function to load read status from localStorage
  const loadReadStatus = useCallback((): Record<string, boolean> => {
    try {
      const storedData = localStorage.getItem(STORAGE.READ_STATUS_KEY);
      return storedData ? JSON.parse(storedData) : {};
    } catch (error) {
      console.error('Error loading read status from localStorage:', error);
      return {};
    }
  }, []);

  // Function to save read status to localStorage
  const saveReadStatus = useCallback((emailId: string, isRead: boolean): void => {
    try {
      const storedData = localStorage.getItem(STORAGE.READ_STATUS_KEY);
      const readStatus = storedData ? JSON.parse(storedData) : {};

      readStatus[emailId] = isRead;

      localStorage.setItem(STORAGE.READ_STATUS_KEY, JSON.stringify(readStatus));
    } catch (error) {
      console.error('Error saving read status to localStorage:', error);
    }
  }, []);

  // Function to select an email
  const selectEmail = useCallback((emailId: string): void => {
    const email = emails.find(e => e.id === emailId);

    if (email) {
      setSelectedEmail(email);

      // Mark the email as read if it's not already
      if (!email.isRead) {
        markAsRead(emailId);
      }

      // Track email opened event for analytics
      if (sessionId && sessionInitialized) {
        try {
          const deviceType = typeof navigator !== 'undefined' && /Mobile|Android|iPhone|iPad/.test(navigator.userAgent) ? 'mobile' : 'desktop';
          const browser = typeof navigator !== 'undefined' && navigator.userAgent.includes('Chrome') ? 'Chrome' :
                         typeof navigator !== 'undefined' && navigator.userAgent.includes('Firefox') ? 'Firefox' :
                         typeof navigator !== 'undefined' && navigator.userAgent.includes('Safari') ? 'Safari' : 'Unknown';

          trackEmailOpened(sessionId, emailId, {
            deviceType,
            browser,
            country: 'unknown',
            emailSubject: email.subject,
            emailSender: email.sender,
          }).catch(err => {
            console.error('Failed to track email opened in analytics system:', err);
          });
        } catch (error) {
          console.error('Unexpected error in analytics email opened tracking:', error);
        }
      }
    }
  }, [emails, sessionId, sessionInitialized]);

  // Function to mark an email as read
  const markAsRead = useCallback((emailId: string): void => {
    // Save to localStorage
    saveReadStatus(emailId, true);

    // Update state
    setEmails(prevEmails =>
      prevEmails.map(email =>
        email.id === emailId ? { ...email, isRead: true } : email
      )
    );

    // Update selected email if it's the one being marked as read
    setSelectedEmail(prevSelected =>
      prevSelected?.id === emailId
        ? { ...prevSelected, isRead: true }
        : prevSelected
    );
  }, [saveReadStatus, setEmails]);

  // Function to clear the selected email
  const clearSelectedEmail = useCallback((): void => {
    setSelectedEmail(null);
  }, []);

  // Function to abort any in-flight delete requests
  const abortDeleteRequests = useCallback((): void => {
    if (deleteAbortControllerRef.current) {
      console.log(`[${new Date().toISOString()}] Aborting in-flight delete request`);
      deleteAbortControllerRef.current.abort();
      deleteAbortControllerRef.current = null;
    }

    // Reject all pending deletions
    if (pendingDeletionsRef.current.length > 0) {
      console.log(`[${new Date().toISOString()}] Rejecting ${pendingDeletionsRef.current.length} pending deletions`);

      // Create a copy of the pending deletions
      const pendingDeletions = [...pendingDeletionsRef.current];

      // Clear the pending deletions
      pendingDeletionsRef.current = [];

      // Reject all pending deletions
      pendingDeletions.forEach(deletion => {
        deletion.reject(new Error('Delete request was aborted'));
      });
    }

    // Reset the mutex lock
    isDeletingRef.current = false;
  }, []);

  // Function to clear the deleted emails set when generating a new address
  const clearDeletedEmailsSet = useCallback((): void => {
    console.log(`[${new Date().toISOString()}] Clearing deleted emails set. Previous size: ${deletedEmailIdsRef.current.size}`);
    deletedEmailIdsRef.current.clear();
  }, []);

  // Function to filter out deleted emails from a list
  const filterOutDeletedEmails = useCallback((emailsList: Email[]): Email[] => {
    if (deletedEmailIdsRef.current.size === 0) {
      return emailsList; // No deleted emails to filter out
    }

    return emailsList.filter(email => !deletedEmailIdsRef.current.has(email.id));
  }, []);

  // Function to filter out emails from a previous address during transition
  const filterDuringAddressTransition = useCallback((
    emailsList: Email[],
    currentAddress: string | null,
    previousAddress: string | null
  ): Email[] => {
    // If we're not in a transition or there's no previous address, just return the emails
    if (!previousAddress || !currentAddress) {
      return emailsList;
    }

    // During address transition, we need to be extremely aggressive with filtering
    // Only keep emails that we're 100% sure belong to the current address
    const filteredEmails = emailsList.filter(email => {
      // First, filter out any deleted emails
      if (deletedEmailIdsRef.current.has(email.id)) {
        return false;
      }

      // Check if this is a guide email (these are always safe to show)
      if (email.id.startsWith('guide-')) {
        return true;
      }

      // Check the email creation time - if it's very recent, it's likely for the new address
      const emailDate = new Date(email.date);
      const now = new Date();
      const timeDiffSeconds = (now.getTime() - emailDate.getTime()) / 1000;

      // If the email was created in the last 5 seconds, it's likely for the new address
      // This helps with real-time updates that might come in right after address generation
      if (timeDiffSeconds < 5) {
        console.log(`[${new Date().toISOString()}] Keeping very recent email (${timeDiffSeconds.toFixed(2)}s old):`, email.id);
        return true;
      }

      // If the email has a recipient field, check if it matches the current address
      if (email.to && typeof email.to === 'string') {
        // If the email is addressed to the previous address, filter it out
        if (email.to.toLowerCase().includes(previousAddress.toLowerCase())) {
          console.log(`[${new Date().toISOString()}] Filtering out email addressed to previous address:`, email.id);
          return false;
        }

        // If the email is addressed to the current address, keep it
        if (email.to.toLowerCase().includes(currentAddress.toLowerCase())) {
          return true;
        }
      }

      // Check the email content for mentions of the addresses
      if (email.content && typeof email.content === 'string') {
        // If the content contains the previous address, it's likely for the previous address
        if (email.content.toLowerCase().includes(previousAddress.toLowerCase())) {
          console.log(`[${new Date().toISOString()}] Filtering out email with content containing previous address:`, email.id);
          return false;
        }

        // If the content contains the current address and not the previous, it's likely for the current address
        if (email.content.toLowerCase().includes(currentAddress.toLowerCase())) {
          return true;
        }
      }

      // If we can't determine for sure, be conservative during transition and filter it out
      return false;
    });

    console.log(`[${new Date().toISOString()}] Address transition filtering: ${emailsList.length} -> ${filteredEmails.length} emails`);
    return filteredEmails;
  }, []);

  // Function to process the next deletion in the queue
  const processNextDeletion = useCallback(async (): Promise<void> => {
    // If there are no pending deletions or a deletion is already in progress, return
    if (pendingDeletionsRef.current.length === 0 || isDeletingRef.current) {
      return;
    }

    // Set the mutex lock
    isDeletingRef.current = true;

    // Get the next deletion from the queue
    const nextDeletion = pendingDeletionsRef.current.shift();
    if (!nextDeletion) {
      isDeletingRef.current = false;
      return;
    }

    try {
      // Get the current email address from localStorage
      const emailAddress = localStorage.getItem(STORAGE.EMAIL_ADDRESS_KEY);

      if (!emailAddress) {
        throw new Error('Cannot delete email: No email address found');
      }

      // Get the auto-refresh manager instance
      const autoRefreshManager = AutoRefreshManager.getInstance();

      // Create a new abort controller for this request
      deleteAbortControllerRef.current = new AbortController();
      const signal = deleteAbortControllerRef.current.signal;

      // Pause auto-refresh during email deletion
      console.log(`[${new Date().toISOString()}] Pausing auto-refresh for email deletion`);
      autoRefreshManager.pauseAutoRefresh();

      // Call the API to delete the email from the database
      console.log(`[${new Date().toISOString()}] Processing queued deletion for email ID ${nextDeletion.emailId}`);
      const response = await fetch(`/api/emails/${encodeURIComponent(emailAddress)}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ emailId: nextDeletion.emailId }),
        signal // Pass the abort signal to the fetch request
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(`Error deleting email: ${data.message}`);
      }

      console.log(`[${new Date().toISOString()}] Email deleted successfully from queue`);
      nextDeletion.resolve();
    } catch (error) {
      // Check if the request was aborted
      if (error instanceof DOMException && error.name === 'AbortError') {
        console.log(`[${new Date().toISOString()}] Queued delete request was aborted`);
        nextDeletion.reject(new Error('Delete request was aborted'));
      } else {
        console.error(`[${new Date().toISOString()}] Error processing queued deletion:`, error);
        nextDeletion.reject(error instanceof Error ? error : new Error(String(error)));
      }
    } finally {
      // Clear the abort controller reference
      deleteAbortControllerRef.current = null;

      // Release the mutex lock
      isDeletingRef.current = false;

      // Get the auto-refresh manager instance
      const autoRefreshManager = AutoRefreshManager.getInstance();

      // Resume auto-refresh after a delay
      console.log(`[${new Date().toISOString()}] Resuming auto-refresh after queued email deletion with ${TIMEOUTS.EMAIL_DELETION_DELAY}ms delay`);
      autoRefreshManager.resumeAutoRefresh(TIMEOUTS.EMAIL_DELETION_DELAY);

      // Process the next deletion in the queue
      setTimeout(() => {
        processNextDeletion();
      }, 100); // Small delay to prevent overwhelming the server
    }
  }, []);

  // Function to delete an email
  const deleteEmail = useCallback(async (emailId: string, _e?: React.MouseEvent): Promise<void> => {
    // Get the current email address from localStorage
    const emailAddress = localStorage.getItem(STORAGE.EMAIL_ADDRESS_KEY);

    if (!emailAddress) {
      logError('deleteEmail', 'Cannot delete email: No email address found');
      return;
    }

    // Add the email ID to the deleted set to prevent it from reappearing
    deletedEmailIdsRef.current.add(emailId);
    console.log(`[${new Date().toISOString()}] Added email ID ${emailId} to deleted set. Current size: ${deletedEmailIdsRef.current.size}`);

    // Optimistically update UI first for better user experience
    // Remove from state, ensuring all emails in the deleted set are filtered out
    setEmails(prevEmails => prevEmails.filter(email => !deletedEmailIdsRef.current.has(email.id)));

    // Clear selection if the deleted email was selected
    if (selectedEmail?.id === emailId) {
      setSelectedEmail(null);
    }

    // Track email deleted event for analytics
    if (sessionId && sessionInitialized) {
      try {
        const email = emails.find(e => e.id === emailId);
        const deviceType = typeof navigator !== 'undefined' && /Mobile|Android|iPhone|iPad/.test(navigator.userAgent) ? 'mobile' : 'desktop';
        const browser = typeof navigator !== 'undefined' && navigator.userAgent.includes('Chrome') ? 'Chrome' :
                       typeof navigator !== 'undefined' && navigator.userAgent.includes('Firefox') ? 'Firefox' :
                       typeof navigator !== 'undefined' && navigator.userAgent.includes('Safari') ? 'Safari' : 'Unknown';

        trackEmailDeleted(sessionId, emailId, {
          deviceType,
          browser,
          country: 'unknown',
          emailSubject: email?.subject,
        }).catch(err => {
          console.error('Failed to track email deleted in analytics system:', err);
        });
      } catch (error) {
        console.error('Unexpected error in analytics email deleted tracking:', error);
      }
    }

    // Create a promise that will be resolved when the deletion is complete
    const deletionPromise = new Promise<void>((resolve, reject) => {
      // Add the deletion to the queue
      pendingDeletionsRef.current.push({
        emailId,
        resolve,
        reject
      });

      console.log(`[${new Date().toISOString()}] Added email ID ${emailId} to deletion queue. Queue size: ${pendingDeletionsRef.current.length}`);

      // Start processing the queue if it's not already being processed
      processNextDeletion().catch(error => {
        console.error(`[${new Date().toISOString()}] Error processing deletion queue:`, error);
      });
    });

    try {
      // Wait for the deletion to complete
      await deletionPromise;
    } catch (error) {
      // The error will be logged by the queue processor
      console.log(`[${new Date().toISOString()}] Deletion promise rejected for email ID ${emailId}:`, error);
      // We don't rethrow the error because we've already updated the UI optimistically
    }
  }, [selectedEmail, setEmails, processNextDeletion, sessionId, sessionInitialized, emails]);

  return {
    selectedEmail,
    selectEmail,
    markAsRead,
    deleteEmail,
    loadReadStatus,
    saveReadStatus,
    clearSelectedEmail,
    abortDeleteRequests,
    clearDeletedEmailsSet,
    filterOutDeletedEmails,
    filterDuringAddressTransition
  };
};
